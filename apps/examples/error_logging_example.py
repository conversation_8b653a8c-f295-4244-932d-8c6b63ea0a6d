#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : error_logging_example.py
# @Update  : 2025/8/7 12:00 错误日志记录示例

"""
错误日志记录示例

演示如何正确记录错误信息和堆栈跟踪
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from configs.logging_config import (
    setup_logging, 
    get_app_logger, 
    get_db_logger,
    log_exception,
    log_error_with_context,
    log_critical_error
)


def setup_example_logging():
    """设置示例日志配置"""
    log_config = {
        'level': 'DEBUG',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    }
    
    # 创建示例日志目录
    log_dir = project_root / "logs" / "examples"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    setup_logging(log_config, str(log_dir))


def example_basic_error_logging():
    """基本错误日志记录示例"""
    logger = get_app_logger()
    
    print("=== 基本错误日志记录示例 ===")
    
    try:
        # 故意引发一个错误
        result = 10 / 0
    except ZeroDivisionError as e:
        # 方法1: 使用 exc_info=True 自动包含堆栈信息
        logger.error("除零错误发生", exc_info=True)
        
        # 方法2: 使用便捷函数
        log_exception(logger, "使用便捷函数记录除零错误", e)


def example_context_error_logging():
    """带上下文的错误日志记录示例"""
    print("\n=== 带上下文的错误日志记录示例 ===")
    
    user_id = "user_123"
    operation = "calculate_result"
    
    try:
        # 模拟一个复杂的操作
        data = {"numbers": [1, 2, 0]}
        result = 100 / data["numbers"][2]
    except Exception as e:
        # 记录带上下文的错误
        log_error_with_context(
            'app',
            f"操作 {operation} 失败",
            exception=e,
            user_id=user_id,
            operation=operation,
            data=data
        )


def example_database_error():
    """数据库错误日志记录示例"""
    print("\n=== 数据库错误日志记录示例 ===")
    
    db_logger = get_db_logger()
    
    try:
        # 模拟数据库连接错误
        raise ConnectionError("无法连接到数据库服务器 localhost:5432")
    except ConnectionError as e:
        log_exception(
            db_logger,
            "数据库连接失败",
            e,
            host="localhost",
            port=5432,
            database="echonote",
            retry_count=3
        )


def example_critical_error():
    """严重错误日志记录示例"""
    print("\n=== 严重错误日志记录示例 ===")
    
    try:
        # 模拟系统级错误
        raise SystemError("系统内存不足，无法继续执行")
    except SystemError as e:
        log_critical_error(
            "系统级错误发生，应用可能无法正常运行",
            exception=e,
            memory_usage="95%",
            available_memory="256MB",
            required_memory="1GB"
        )


def example_nested_exception():
    """嵌套异常日志记录示例"""
    print("\n=== 嵌套异常日志记录示例 ===")
    
    logger = get_app_logger()
    
    def inner_function():
        """内部函数，会抛出异常"""
        raise ValueError("内部函数中的值错误")
    
    def middle_function():
        """中间函数，捕获并重新抛出异常"""
        try:
            inner_function()
        except ValueError as e:
            # 记录内部异常
            log_exception(logger, "中间函数捕获到内部异常", e)
            # 重新抛出新的异常
            raise RuntimeError("中间函数处理失败") from e
    
    try:
        middle_function()
    except RuntimeError as e:
        # 记录最终异常，包含完整的异常链
        log_exception(logger, "顶层函数捕获到运行时异常", e)


def example_async_error():
    """异步操作错误日志记录示例"""
    print("\n=== 异步操作错误日志记录示例 ===")
    
    import asyncio
    
    async def async_operation():
        """模拟异步操作"""
        await asyncio.sleep(0.1)
        raise TimeoutError("异步操作超时")
    
    async def handle_async_error():
        """处理异步错误"""
        logger = get_app_logger()
        
        try:
            await async_operation()
        except TimeoutError as e:
            log_exception(
                logger,
                "异步操作失败",
                e,
                operation="async_operation",
                timeout_seconds=30,
                retry_count=3
            )
    
    # 运行异步示例
    asyncio.run(handle_async_error())


def example_custom_exception():
    """自定义异常日志记录示例"""
    print("\n=== 自定义异常日志记录示例 ===")
    
    class BusinessLogicError(Exception):
        """业务逻辑异常"""
        def __init__(self, message, error_code=None, details=None):
            super().__init__(message)
            self.error_code = error_code
            self.details = details or {}
    
    logger = get_app_logger()
    
    try:
        # 抛出自定义异常
        raise BusinessLogicError(
            "用户权限不足",
            error_code="PERMISSION_DENIED",
            details={
                "user_id": "user_123",
                "required_permission": "admin",
                "current_permission": "user"
            }
        )
    except BusinessLogicError as e:
        log_exception(
            logger,
            "业务逻辑错误",
            e,
            error_code=getattr(e, 'error_code', None),
            details=getattr(e, 'details', {})
        )


def example_unhandled_exception():
    """未处理异常示例（将被全局异常处理器捕获）"""
    print("\n=== 未处理异常示例 ===")
    print("注意：这将触发全局异常处理器")
    
    # 故意不捕获这个异常，让全局处理器处理
    # raise RuntimeError("这是一个未处理的异常，将被全局异常处理器记录")
    print("（为了不中断示例，已注释掉未处理异常代码）")


def main():
    """主函数"""
    print("错误日志记录示例")
    print("=" * 50)
    
    # 设置日志
    setup_example_logging()
    
    # 运行各种示例
    example_basic_error_logging()
    example_context_error_logging()
    example_database_error()
    example_critical_error()
    example_nested_exception()
    example_async_error()
    example_custom_exception()
    example_unhandled_exception()
    
    print("\n" + "=" * 50)
    print("所有示例已完成，请查看日志文件:")
    print(f"- 应用日志: {project_root}/logs/examples/app.log")
    print(f"- 错误日志: {project_root}/logs/examples/error.log")


if __name__ == "__main__":
    main()
